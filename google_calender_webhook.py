import os
import json
import hmac
import hashlib
import uuid
from datetime import datetime, timezone
from flask import Flask, request, jsonify
from threading import Thread
import requests

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

# --- CONFIGURATION ---
SCOPES = ["https://www.googleapis.com/auth/calendar.readonly"]
# Your webhook endpoint URL (must be HTTPS and publicly accessible)
WEBHOOK_URL = "https://f1b0-103-173-221-201.ngrok-free.app/webhook/calendar"
# Secret token for webhook verification (optional but recommended)
WEBHOOK_SECRET = "your-secret-key-here"
# Calendar ID to watch (use 'primary' for the user's primary calendar)
CALENDAR_ID = "primary"
# Webhook expiration time in seconds (max 604800 = 7 days for Google Calendar)
WEBHOOK_TTL = 604800

app = Flask(__name__)

# Store active watch channels
active_channels = {}


def authenticate():
    """Handles user authentication and returns an authorized service object."""
    creds = None
    if os.path.exists("token.json"):
        creds = Credentials.from_authorized_user_file("token.json", SCOPES)

    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            print("Refreshing expired credentials...")
            creds.refresh(Request())
        else:
            from google_auth_oauthlib.flow import InstalledAppFlow

            flow = InstalledAppFlow.from_client_secrets_file(
                "data/credentials.json", SCOPES
            )
            creds = flow.run_local_server(port=0)

        with open("token.json", "w") as token:
            token.write(creds.to_json())
            print("Credentials saved to token.json")

    print("Authentication successful")

    return build("calendar", "v3", credentials=creds)


def verify_webhook_signature(payload, signature, secret):
    """Verify the webhook signature for security."""
    if not signature or not secret:
        return True  # Skip verification if no secret is configured

    expected_signature = hmac.new(
        secret.encode("utf-8"), payload, hashlib.sha256
    ).hexdigest()

    return hmac.compare_digest(signature, expected_signature)


def setup_webhook_watch(service):
    """Set up a webhook watch for calendar events."""
    try:
        # Generate a unique channel ID
        channel_id = str(uuid.uuid4())

        # Calculate expiration time (current time + TTL in milliseconds)
        expiration = int((datetime.now(timezone.utc).timestamp() + WEBHOOK_TTL) * 1000)

        watch_request = {
            "id": channel_id,
            "type": "web_hook",
            "address": WEBHOOK_URL,
            "expiration": str(expiration),
        }

        # Add token for verification if secret is configured
        if WEBHOOK_SECRET:
            watch_request["token"] = WEBHOOK_SECRET

        # Create the watch
        watch_response = (
            service.events().watch(calendarId=CALENDAR_ID, body=watch_request).execute()
        )

        # Store the channel info for later cleanup
        active_channels[channel_id] = {
            "resource_id": watch_response["resourceId"],
            "expiration": expiration,
        }

        print(f"Webhook watch created successfully!")
        print(f"Channel ID: {channel_id}")
        print(f"Resource ID: {watch_response['resourceId']}")
        print(f"Expires: {datetime.fromtimestamp(expiration/1000, timezone.utc)}")

        return channel_id, watch_response["resourceId"]

    except HttpError as error:
        print(f"An error occurred setting up webhook: {error}")
        return None, None


def stop_webhook_watch(service, channel_id, resource_id):
    """Stop a webhook watch."""
    try:
        service.channels().stop(
            body={"id": channel_id, "resourceId": resource_id}
        ).execute()

        # Remove from active channels
        if channel_id in active_channels:
            del active_channels[channel_id]

        print(f"Webhook watch stopped for channel: {channel_id}")

    except HttpError as error:
        print(f"An error occurred stopping webhook: {error}")


def get_event_details(service, event_id):
    """Fetch detailed information about a specific event."""
    try:
        event = service.events().get(calendarId=CALENDAR_ID, eventId=event_id).execute()
        return event
    except HttpError as error:
        print(f"Error fetching event details: {error}")
        return None


def process_calendar_event(service, notification_data):
    """Process the calendar event notification."""
    # The webhook notification doesn't contain event details,
    # only that something changed. We need to fetch recent events
    # to determine what actually happened.

    try:
        # Get recent events (last 5 minutes)
        now = datetime.now(timezone.utc)
        time_min = now.replace(microsecond=0).isoformat().replace("+00:00", "Z")

        events_result = (
            service.events()
            .list(
                calendarId=CALENDAR_ID,
                timeMin=time_min,
                maxResults=10,
                singleEvents=True,
                orderBy="startTime",
            )
            .execute()
        )

        events = events_result.get("items", [])

        print(f"Found {len(events)} recent events to process")

        for event in events:
            event_id = event["id"]
            summary = event.get("summary", "No title")
            start = event["start"].get("dateTime", event["start"].get("date"))
            created = event.get("created")
            updated = event.get("updated")

            # THIS IS THE "ACTION" PART OF THE WEBHOOK
            print(f"  -> ACTION: Processing event: '{summary}' starting at {start}")
            print(f"     Event ID: {event_id}")
            print(f"     Created: {created}")
            print(f"     Last Updated: {updated}")

            # Add your custom logic here
            # For example:
            # - Send notifications
            # - Update databases
            # - Trigger other workflows
            # - Send emails
            # - Call other APIs

    except HttpError as error:
        print(f"Error processing calendar events: {error}")


@app.route("/webhook/calendar", methods=["POST"])
def calendar_webhook():
    """Handle incoming webhook notifications from Google Calendar."""

    # Verify the request signature if secret is configured
    if WEBHOOK_SECRET:
        signature = request.headers.get("X-Goog-Channel-Token")
        if not verify_webhook_signature(request.data, signature, WEBHOOK_SECRET):
            print("Invalid webhook signature")
            return jsonify({"error": "Invalid signature"}), 401

    # Extract notification headers
    channel_id = request.headers.get("X-Goog-Channel-ID")
    channel_token = request.headers.get("X-Goog-Channel-Token")
    resource_id = request.headers.get("X-Goog-Resource-ID")
    resource_state = request.headers.get("X-Goog-Resource-State")
    resource_uri = request.headers.get("X-Goog-Resource-URI")

    print(
        f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Webhook notification received"
    )
    print(f"Channel ID: {channel_id}")
    print(f"Resource ID: {resource_id}")
    print(f"Resource State: {resource_state}")
    print(f"Resource URI: {resource_uri}")

    # Process the notification in a separate thread to avoid blocking
    if resource_state in ["exists", "sync"]:
        thread = Thread(target=handle_notification, args=(channel_id, resource_state))
        thread.daemon = True
        thread.start()

    return jsonify({"status": "received"}), 200


def handle_notification(channel_id, resource_state):
    """Handle the webhook notification in a separate thread."""
    try:
        service = authenticate()

        notification_data = {
            "channel_id": channel_id,
            "resource_state": resource_state,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        process_calendar_event(service, notification_data)

    except Exception as e:
        print(f"Error handling notification: {e}")


@app.route("/webhook/setup", methods=["POST"])
def setup_webhook():
    """Endpoint to set up the webhook watch."""
    try:
        service = authenticate()
        channel_id, resource_id = setup_webhook_watch(service)

        if channel_id:
            return jsonify(
                {
                    "status": "success",
                    "channel_id": channel_id,
                    "resource_id": resource_id,
                }
            )
        else:
            return (
                jsonify({"status": "error", "message": "Failed to set up webhook"}),
                500,
            )

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500


@app.route("/webhook/stop/<channel_id>", methods=["POST"])
def stop_webhook(channel_id):
    """Endpoint to stop a webhook watch."""
    try:
        if channel_id not in active_channels:
            return jsonify({"status": "error", "message": "Channel not found"}), 404

        service = authenticate()
        resource_id = active_channels[channel_id]["resource_id"]
        stop_webhook_watch(service, channel_id, resource_id)

        return jsonify({"status": "success", "message": "Webhook stopped"})

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500


@app.route("/webhook/status", methods=["GET"])
def webhook_status():
    """Get the status of active webhooks."""
    status = []
    current_time = datetime.now(timezone.utc).timestamp() * 1000

    for channel_id, channel_info in active_channels.items():
        expiration = channel_info["expiration"]
        is_expired = current_time > expiration

        status.append(
            {
                "channel_id": channel_id,
                "resource_id": channel_info["resource_id"],
                "expires_at": datetime.fromtimestamp(
                    expiration / 1000, timezone.utc
                ).isoformat(),
                "is_expired": is_expired,
            }
        )

    return jsonify({"active_channels": status})


def cleanup_expired_channels():
    """Clean up expired webhook channels."""
    try:
        service = authenticate()
        current_time = datetime.now(timezone.utc).timestamp() * 1000
        expired_channels = []

        for channel_id, channel_info in active_channels.items():
            if current_time > channel_info["expiration"]:
                expired_channels.append(channel_id)

        for channel_id in expired_channels:
            resource_id = active_channels[channel_id]["resource_id"]
            stop_webhook_watch(service, channel_id, resource_id)
            print(f"Cleaned up expired channel: {channel_id}")

    except Exception as e:
        print(f"Error during cleanup: {e}")


if __name__ == "__main__":
    print("--- Google Calendar Webhook Implementation ---")
    print(f"Webhook URL: {WEBHOOK_URL}")
    print(f"Calendar ID: {CALENDAR_ID}")
    print("\nEndpoints:")
    print("  POST /webhook/calendar - Receive webhook notifications")
    print("  POST /webhook/setup - Set up webhook watch")
    print("  POST /webhook/stop/<channel_id> - Stop webhook watch")
    print("  GET /webhook/status - Get webhook status")
    print("\nStarting Flask server...")

    # Run cleanup on startup
    cleanup_expired_channels()

    # Start the Flask app
    app.run(host="0.0.0.0", port=8000, debug=False)
