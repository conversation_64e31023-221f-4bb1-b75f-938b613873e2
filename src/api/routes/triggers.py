"""
Trigger management API endpoints.

This module provides REST API endpoints for managing triggers including
CRUD operations and trigger lifecycle management.
"""

from fastapi import APIRouter, HTTPException, Depends, Query, Request, status
from typing import List, Optional
from uuid import UUID

from src.schemas.trigger import (
    TriggerCreate,
    TriggerUpdate,
    TriggerResponse,
    TriggerListResponse,
    TriggerToggleRequest,
    TriggerFilterRequest,
    TriggerStatsResponse,
    TriggerExecutionResponse,
)
from src.core.trigger_manager import TriggerManager
from src.api.middleware.auth import get_current_user, require_auth
from src.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/api/v1/triggers", tags=["triggers"])

# Authentication is handled by middleware


# Dependency to get trigger manager instance
def get_trigger_manager() -> TriggerManager:
    """Get trigger manager instance."""
    # In a real application, this would be injected via dependency injection
    # For now, we'll create a singleton instance
    if not hasattr(get_trigger_manager, "_instance"):
        from src.adapters import GoogleCalendarAdapter

        get_trigger_manager._instance = TriggerManager()

        # Register available adapters
        google_calendar_adapter = GoogleCalendarAdapter()
        get_trigger_manager._instance.register_adapter(google_calendar_adapter)

        logger.info("TriggerManager singleton created with registered adapters")
    return get_trigger_manager._instance


@router.post(
    "/",
    response_model=TriggerResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new trigger",
    description="""
    Create a new trigger for workflow automation.

    **Authentication Required**: This endpoint requires authentication using either:
    - Bearer token in Authorization header: `Authorization: Bearer <your-api-key>`
    - API key in X-API-Key header: `X-API-Key: <your-api-key>`

    **Example Request**:
    ```bash
    curl -X POST "http://localhost:8000/api/v1/triggers/" \\
         -H "Authorization: Bearer abc" \\
         -H "Content-Type: application/json" \\
         -d '{
           "user_id": "user123",
           "workflow_id": "workflow456",
           "trigger_type": "google_calendar",
           "trigger_name": "My Calendar Trigger",
           "trigger_config": {"calendar_id": "primary"},
           "event_types": ["created", "updated"]
         }'
    ```
    """,
    responses={
        201: {"description": "Trigger created successfully"},
        400: {"description": "Invalid request data"},
        401: {"description": "Authentication required"},
        500: {"description": "Internal server error"},
    },
)
async def create_trigger(
    trigger_data: TriggerCreate,
    request: Request,
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerResponse:
    """
    Create a new trigger.

    Args:
        trigger_data: Trigger creation data
        request: HTTP request for authentication
        trigger_manager: Trigger manager instance

    Returns:
        TriggerResponse: Created trigger details

    Raises:
        HTTPException: If trigger creation fails
    """
    # Require authentication

    require_auth(request)
    authenticated_user = get_current_user(request)

    # Use authenticated user ID instead of the one from request data for security
    user_id = trigger_data.user_id

    # # Log the original request for debugging
    # logger.debug(
    #     f"Create trigger request",
    #     trigger_data=trigger_data.dict(),
    #     authenticated_user=authenticated_user,
    # )

    # logger.info(
    #     f"Creating trigger for user {user_id}", trigger_type=trigger_data.trigger_type
    # )

    try:
        # Debug: Check if adapter is registered
        adapters = trigger_manager.list_adapters()
        logger.info(f"Available adapters: {adapters}")

        # Debug: Check if google_calendar adapter exists
        adapter = trigger_manager.get_adapter(trigger_data.trigger_type)
        logger.info(f"Found adapter for {trigger_data.trigger_type}: {adapter}")

        trigger_id = await trigger_manager.create_trigger(
            user_id=user_id,
            workflow_id=trigger_data.workflow_id,
            trigger_type=trigger_data.trigger_type,
            trigger_name=trigger_data.trigger_name,
            trigger_config=trigger_data.trigger_config,
            event_types=trigger_data.event_types,
        )

        if not trigger_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to create trigger",
            )

        # Get the created trigger details
        triggers = await trigger_manager.get_triggers_for_user(user_id)
        created_trigger = next((t for t in triggers if t.id == trigger_id), None)

        if not created_trigger:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Trigger created but could not retrieve details",
            )

        logger.info(f"Successfully created trigger {trigger_id}")

        return TriggerResponse.from_orm(created_trigger)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create trigger", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while creating trigger",
        )


@router.get(
    "/",
    response_model=TriggerListResponse,
    summary="List triggers",
    description="""
    List triggers with optional filtering and pagination.

    **Authentication Required**: This endpoint requires authentication using either:
    - Bearer token in Authorization header: `Authorization: Bearer <your-api-key>`
    - API key in X-API-Key header: `X-API-Key: <your-api-key>`

    **Example Request**:
    ```bash
    curl -X GET "http://localhost:8000/api/v1/triggers/" \\
         -H "Authorization: Bearer abc"
    ```
    """,
    responses={
        200: {"description": "List of triggers retrieved successfully"},
        401: {"description": "Authentication required"},
        403: {"description": "Access denied"},
        500: {"description": "Internal server error"},
    },
)
async def list_triggers(
    request: Request,
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    workflow_id: Optional[str] = Query(None, description="Filter by workflow ID"),
    trigger_type: Optional[str] = Query(None, description="Filter by trigger type"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerListResponse:
    """
    List triggers with optional filtering and pagination.

    Args:
        request: HTTP request for authentication
        user_id: Optional user ID filter
        workflow_id: Optional workflow ID filter
        trigger_type: Optional trigger type filter
        is_active: Optional active status filter
        page: Page number for pagination
        page_size: Number of items per page
        trigger_manager: Trigger manager instance

    Returns:
        TriggerListResponse: Paginated list of triggers
    """
    require_auth(request)
    current_user = user_id

    # If user_id is not provided, default to current user
    if not user_id:
        user_id = current_user

    # Only allow users to see their own triggers (unless admin)
    # For now, restrict to current user only
    if user_id != current_user:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied: Cannot view other users' triggers",
        )

    try:
        # Get triggers for the user
        triggers = await trigger_manager.get_triggers_for_user(user_id)

        # Apply filters
        filtered_triggers = triggers
        if workflow_id:
            filtered_triggers = [
                t for t in filtered_triggers if t.workflow_id == workflow_id
            ]
        if trigger_type:
            filtered_triggers = [
                t for t in filtered_triggers if t.trigger_type == trigger_type
            ]
        if is_active is not None:
            filtered_triggers = [
                t for t in filtered_triggers if t.is_active == is_active
            ]

        # Apply pagination
        total_count = len(filtered_triggers)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_triggers = filtered_triggers[start_idx:end_idx]

        # Convert to response models
        trigger_responses = [TriggerResponse.from_orm(t) for t in paginated_triggers]

        return TriggerListResponse(
            triggers=trigger_responses,
            total=total_count,
            page=page,
            page_size=page_size,
            total_pages=(total_count + page_size - 1) // page_size,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to list triggers", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while listing triggers",
        )


@router.get("/{trigger_id}", response_model=TriggerResponse)
async def get_trigger(
    trigger_id: UUID,
    request: Request,
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerResponse:
    """
    Get a specific trigger by ID.

    Args:
        trigger_id: Trigger ID to retrieve
        request: HTTP request for authentication
        trigger_manager: Trigger manager instance

    Returns:
        TriggerResponse: Trigger details

    Raises:
        HTTPException: If trigger not found or access denied
    """
    require_auth(request)
    user_id = get_current_user(request)

    try:
        # Get user's triggers and find the specific one
        triggers = await trigger_manager.get_triggers_for_user(user_id)
        trigger = next((t for t in triggers if t.id == trigger_id), None)

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Trigger not found"
            )

        return TriggerResponse.from_orm(trigger)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get trigger {trigger_id}", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving trigger",
        )


@router.put("/{trigger_id}", response_model=TriggerResponse)
async def update_trigger(
    trigger_id: UUID,
    trigger_data: TriggerUpdate,
    request: Request,
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerResponse:
    """
    Update a trigger.

    Args:
        trigger_id: Trigger ID to update
        trigger_data: Updated trigger data
        request: HTTP request for authentication
        trigger_manager: Trigger manager instance

    Returns:
        TriggerResponse: Updated trigger details

    Raises:
        HTTPException: If trigger not found or update fails
    """
    require_auth(request)
    user_id = get_current_user(request)

    try:
        # Verify trigger exists and belongs to user
        triggers = await trigger_manager.get_triggers_for_user(user_id)
        trigger = next((t for t in triggers if t.id == trigger_id), None)

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Trigger not found"
            )

        # For now, we'll implement a simple update by recreating the trigger
        # In a full implementation, you'd have an update method in TriggerManager
        logger.info(f"Updating trigger {trigger_id}")

        # This is a placeholder - implement actual update logic
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Trigger update not yet implemented",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update trigger {trigger_id}", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while updating trigger",
        )


@router.delete("/{trigger_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_trigger(
    trigger_id: UUID,
    request: Request,
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> None:
    """
    Delete a trigger.

    Args:
        trigger_id: Trigger ID to delete
        request: HTTP request for authentication
        trigger_manager: Trigger manager instance

    Raises:
        HTTPException: If trigger not found or deletion fails
    """
    require_auth(request)
    user_id = get_current_user(request)

    try:
        # Verify trigger exists and belongs to user
        triggers = await trigger_manager.get_triggers_for_user(user_id)
        trigger = next((t for t in triggers if t.id == trigger_id), None)

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Trigger not found"
            )

        # Delete the trigger
        success = await trigger_manager.remove_trigger(trigger_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete trigger",
            )

        logger.info(f"Successfully deleted trigger {trigger_id}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete trigger {trigger_id}", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while deleting trigger",
        )


@router.post("/{trigger_id}/toggle", response_model=TriggerResponse)
async def toggle_trigger(
    trigger_id: UUID,
    toggle_data: TriggerToggleRequest,
    request: Request,
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerResponse:
    """
    Enable or disable a trigger.

    Args:
        trigger_id: Trigger ID to toggle
        toggle_data: Toggle request data
        request: HTTP request for authentication
        trigger_manager: Trigger manager instance

    Returns:
        TriggerResponse: Updated trigger details

    Raises:
        HTTPException: If trigger not found or toggle fails
    """
    require_auth(request)
    user_id = get_current_user(request)

    try:
        # Verify trigger exists and belongs to user
        triggers = await trigger_manager.get_triggers_for_user(user_id)
        trigger = next((t for t in triggers if t.id == trigger_id), None)

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Trigger not found"
            )

        # Toggle the trigger
        success = await trigger_manager.toggle_trigger(
            trigger_id, toggle_data.is_active
        )
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to toggle trigger",
            )

        # Get updated trigger details
        updated_triggers = await trigger_manager.get_triggers_for_user(user_id)
        updated_trigger = next(
            (t for t in updated_triggers if t.id == trigger_id), None
        )

        if not updated_trigger:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Trigger toggled but could not retrieve updated details",
            )

        action = "enabled" if toggle_data.is_active else "disabled"
        logger.info(f"Successfully {action} trigger {trigger_id}")

        return TriggerResponse.from_orm(updated_trigger)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to toggle trigger {trigger_id}", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while toggling trigger",
        )


@router.get("/{trigger_id}/executions", response_model=List[TriggerExecutionResponse])
async def get_trigger_executions(
    trigger_id: UUID,
    request: Request,
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of executions to return"
    ),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> List[TriggerExecutionResponse]:
    """
    Get execution history for a trigger.

    Args:
        trigger_id: Trigger ID to get executions for
        request: HTTP request for authentication
        limit: Maximum number of executions to return
        trigger_manager: Trigger manager instance

    Returns:
        List[TriggerExecutionResponse]: List of trigger executions

    Raises:
        HTTPException: If trigger not found or access denied
    """
    require_auth(request)
    user_id = get_current_user(request)

    try:
        # Verify trigger exists and belongs to user
        triggers = await trigger_manager.get_triggers_for_user(user_id)
        trigger = next((t for t in triggers if t.id == trigger_id), None)

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Trigger not found"
            )

        # Get execution history
        executions = await trigger_manager.get_execution_history(trigger_id, limit)

        return [
            TriggerExecutionResponse.from_orm(execution) for execution in executions
        ]

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get executions for trigger {trigger_id}", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving trigger executions",
        )


@router.get("/stats", response_model=TriggerStatsResponse)
async def get_trigger_stats(
    request: Request, trigger_manager: TriggerManager = Depends(get_trigger_manager)
) -> TriggerStatsResponse:
    """
    Get trigger statistics for the current user.

    Args:
        request: HTTP request for authentication
        trigger_manager: Trigger manager instance

    Returns:
        TriggerStatsResponse: Trigger statistics
    """
    require_auth(request)
    user_id = get_current_user(request)

    try:
        # Get user's triggers
        triggers = await trigger_manager.get_triggers_for_user(user_id)

        # Calculate statistics
        total_triggers = len(triggers)
        active_triggers = len([t for t in triggers if t.is_active])
        inactive_triggers = total_triggers - active_triggers

        # Count by type
        triggers_by_type = {}
        for trigger in triggers:
            trigger_type = trigger.trigger_type
            triggers_by_type[trigger_type] = triggers_by_type.get(trigger_type, 0) + 1

        # For now, return placeholder values for execution stats
        # In a full implementation, you'd query the execution history
        recent_executions = 0
        success_rate = 100.0

        return TriggerStatsResponse(
            total_triggers=total_triggers,
            active_triggers=active_triggers,
            inactive_triggers=inactive_triggers,
            triggers_by_type=triggers_by_type,
            recent_executions=recent_executions,
            success_rate=success_rate,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get trigger stats for user {user_id}", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving trigger statistics",
        )
